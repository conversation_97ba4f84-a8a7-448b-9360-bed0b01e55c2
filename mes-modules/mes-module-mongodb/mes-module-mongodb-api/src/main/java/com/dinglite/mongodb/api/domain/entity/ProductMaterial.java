package com.dinglite.mongodb.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品物料追溯信息实体类，用于记录生产过程中的物料绑定和消耗数据
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductMaterial对象", description = "产品物料追溯信息")
@Document(collection = "product_material")
public class ProductMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB 文档主键（ObjectId 类型）")
    @Id
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "目标集合名称（固定为 product_material）")
    @Field("collection")
    @JsonProperty("collection")
    private String collection = "product_material";

    @ApiModelProperty(value = "产品序列号")
    @Field("sn")
    @JsonProperty("sn")
    private String sn;

    @ApiModelProperty(value = "产线名称")
    @Field("lineName")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @Field("lineCode")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "生产工单号")
    @Field("productionOrder")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "用料工序名称")
    @Field("processName")
    @JsonProperty("processName")
    private String processName;

    @ApiModelProperty(value = "用料工序编码")
    @Field("processCode")
    @JsonProperty("processCode")
    private String processCode;

    @ApiModelProperty(value = "物料名称")
    @Field("materialName")
    @JsonProperty("materialName")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    @Field("materialCode")
    @JsonProperty("materialCode")
    private String materialCode;

    @ApiModelProperty(value = "物料规格")
    @Field("materialSpec")
    @JsonProperty("materialSpec")
    private String materialSpec;

    @ApiModelProperty(value = "供应商名称")
    @Field("supplier")
    @JsonProperty("supplier")
    private String supplier;

    @ApiModelProperty(value = "计量单位")
    @Field("unit")
    @JsonProperty("unit")
    private String unit;

    @ApiModelProperty(value = "BOM标准用量")
    @Field("bomNum")
    @JsonProperty("bomNum")
    private Double bomNum;

    @ApiModelProperty(value = "单包装数量")
    @Field("packageNum")
    @JsonProperty("packageNum")
    private Integer packageNum;

    @ApiModelProperty(value = "物料二维码内容")
    @Field("qrCode")
    @JsonProperty("qrCode")
    private String qrCode;

    @ApiModelProperty(value = "包装箱流水码")
    @Field("boxNumber")
    @JsonProperty("boxNumber")
    private String boxNumber;

    @ApiModelProperty(value = "生产批次号")
    @Field("lotNo")
    @JsonProperty("lotNo")
    private String lotNo;

    @ApiModelProperty(value = "物料生产日期")
    @Field("productDate")
    @JsonProperty("productDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime productDate;

    @ApiModelProperty(value = "物料有效期")
    @Field("expirationDate")
    @JsonProperty("expirationDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expirationDate;

    @ApiModelProperty(value = "上料工站IP地址")
    @Field("stationIP")
    @JsonProperty("stationIP")
    private String stationIP;

    @ApiModelProperty(value = "上料工站名称")
    @Field("stationName")
    @JsonProperty("stationName")
    private String stationName;

    @ApiModelProperty(value = "操作员系统账号")
    @Field("operatorId")
    @JsonProperty("operatorId")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    @Field("operatorName")
    @JsonProperty("operatorName")
    private String operatorName;

    @ApiModelProperty(value = "数据提交时间")
    @Field("submitTime")
    @JsonProperty("submitTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "物料启用时间")
    @Field("startTime")
    @JsonProperty("startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "物料绑定时间")
    @Field("createTime")
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "物料绑定序号")
    @Field("bindIndex")
    @JsonProperty("bindIndex")
    private Integer bindIndex;


}
